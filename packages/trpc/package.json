{"name": "@repo/trpc", "version": "0.0.0", "type": "module", "scripts": {"build": "tsc", "dev": "tsc", "check-types": "tsc --noEmit", "clean": "git clean -xdf .cache .turbo dist node_modules", "lint": "eslint .", "test": "echo \"Error: no test specified\" && exit 1"}, "private": true, "author": "<PERSON>", "packageManager": "pnpm@10.13.1", "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "dependencies": {"@trpc/server": "^11.4.3", "zod": "catalog:"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "prettier": "@repo/prettier"}