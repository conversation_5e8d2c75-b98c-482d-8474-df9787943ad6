{"name": "@repo/prettier", "version": "1.0.0", "description": "", "type": "module", "exports": {".": "./index.js"}, "scripts": {"clean": "git clean -xdf .cache .turbo node_modules"}, "keywords": [], "author": "", "private": "true", "packageManager": "pnpm@10.13.1", "dependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.4.2", "prettier": "catalog:", "prettier-plugin-brace-style": "^0.8.1", "prettier-plugin-merge": "^0.8.0"}, "prettier": "@repo/prettier", "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "catalog:"}}