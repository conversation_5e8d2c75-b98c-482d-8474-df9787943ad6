/** @typedef {import("prettier").Config} PrettierConfig */
/** @typedef {import("@ianvs/prettier-plugin-sort-imports").PluginConfig} SortImportsConfig */

/** @type { PrettierConfig | SortImportsConfig } */
const config = {
    plugins: [
        '@ianvs/prettier-plugin-sort-imports',
        'prettier-plugin-brace-style',
        'prettier-plugin-merge'
    ],
    importOrder: [
        '<TYPES>',
        '<THIRD_PARTY_MODULES>',
        '',
        '<TYPES>^@repo',
        '^@repo/(.*)$',
        '',
        '<TYPES>^[.|..|~]',
        '^~/',
        '^[../]',
        '^[./]'
    ],
    importOrderParserPlugins: ['typescript', 'jsx', 'decorators-legacy'],
    importOrderTypeScriptVersion: '4.0.0',
    trailingComma: 'none',
    tabWidth: 4,
    semi: true,
    singleQuote: true,
    braceStyle: 'allman'
};

export default config;
