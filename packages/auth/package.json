{"name": "@repo/auth", "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "generate": "pnpx @better-auth/cli generate --output ../db/src/schemas/auth.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON>", "private": true, "packageManager": "pnpm@10.13.1", "dependencies": {"@repo/db": "workspace:*", "@repo/prettier": "workspace:^", "better-auth": "^1.3.1"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@repo/prettier"}