import * as path from 'node:path';
import { includeIgnoreFile } from '@eslint/compat';
import js from '@eslint/js';
import turboPlugin from 'eslint-plugin-turbo';
import unicorn from 'eslint-plugin-unicorn';
import tseslint from 'typescript-eslint';

/**
 * A shared ESLint configuration for the repository.
 *
 * @type {import('typescript-eslint').ConfigArray}
 * */
export const config = tseslint.config(
    includeIgnoreFile(path.join(import.meta.dirname, '../../.gitignore')),
    { ignores: ['**/*.config.*'] },
    {
        files: ['**/*.js', '**/*.ts', '**/*.tsx'],
        extends: [
            js.configs.recommended,
            ...tseslint.configs.recommended,
            ...tseslint.configs.recommendedTypeChecked,
            ...tseslint.configs.stylisticTypeChecked
        ],
        plugins: {
            turbo: turboPlugin,
            unicorn
        },
        rules: {
            ...turboPlugin.configs.recommended.rules,
            '@typescript-eslint/no-unused-vars': [
                'error',
                {
                    argsIgnorePattern: '^_',
                    varsIgnorePattern: '^_'
                }
            ],
            '@typescript-eslint/consistent-type-imports': [
                'warn',
                {
                    prefer: 'type-imports',
                    fixStyle: 'separate-type-imports'
                }
            ],
            curly: ['error'],
            'unicorn/filename-case': [
                'error',
                {
                    case: 'kebabCase'
                }
            ]
        }
    },
    {
        linterOptions: {
            reportUnusedDisableDirectives: true
        },
        languageOptions: {
            parserOptions: {
                projectService: true
            }
        }
    }
);
