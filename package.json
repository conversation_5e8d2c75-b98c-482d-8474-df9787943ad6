{"name": "bookmark-plus", "private": true, "scripts": {"build": "turbo run build", "clean": "git clean -xdf node_modules", "dev": "turbo run dev", "lint": "turbo run lint", "check-types": "turbo run check-types"}, "devDependencies": {"@repo/prettier": "workspace:*", "prettier": "catalog:", "turbo": "^2.5.4", "typescript": "catalog:"}, "packageManager": "pnpm@10.13.1", "engines": {"node": "~22"}, "prettier": "@repo/prettier"}