import type { FastifyTRPCPluginOptions } from '@trpc/server/adapters/fastify';
import fastifyCors from '@fastify/cors';
import fastifyRateLimit from '@fastify/rate-limit';
import { fastifyTRPCPlugin } from '@trpc/server/adapters/fastify';
import fastify from 'fastify';

import type { AppRouter } from '@repo/trpc';
import { appRouter } from '@repo/trpc';

const server = fastify({
    maxParamLength: 5000
});

server.register(fastifyRateLimit, {
    max: 1000,
    timeWindow: '1 minute',
    continueExceeding: true
});

server.register(fastifyCors, {
    origin: process.env.CLIENT_ORIGIN ?? 'http://localhost:5173',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
    maxAge: 86400
});

server.register(fastifyTRPCPlugin, {
    prefix: '/trpc',
    trpcOptions: {
        router: appRouter,
        createContext: () => ({})
    } satisfies FastifyTRPCPluginOptions<AppRouter>['trpcOptions']
});

const port = process.env.PORT ? Number(process.env.PORT) : 3000;

server.listen({ port }, (error, address) =>
{
    if (error)
    {
        console.error(error);
        process.exit(1);
    }

    console.log(`Server listening at ${address}`);
});
